<template>
    <div class="home tb-padding-12  border-box" style="background-color: #F2F5F8">
        <div class="font-16 lr-padding-16">
            以下是
            <span class="color-blue">{{ route.query.name }}</span>
            模板企业
        </div>
        <div>
            <CompanySearchList
                :load-data="getData"
                :refresh-data="refreshData"
                :loading="loading"
                :refreshing="refreshing"
                :data="list"
                :total="pageInfo.total"
                :finished="finished"
            />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted, watch } from 'vue'
import type { Ref } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import CompanySearchList from '@/components/enterprise/list/CompanySearchList.vue'
import { useRoute } from 'vue-router'
import type { ICompanyInfo } from '@/types/company'
import aicService from '@/service/aicService'
import type { conditionItem, ISearchGetTemplateItem } from '@/types/company' 
import { useStore } from 'vuex'
import type { IHighSearchRules, ISearchConditions } from '@/types/model'
import { showFailToast } from 'vant'
import { mergeList } from '@/utils/merge-list'

const store = useStore()
const activeTemplete: Ref<ISearchGetTemplateItem | null> = ref(null)
const route = useRoute()
const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
const error = ref(false)
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const list = ref<ICompanyInfo[]>([])
const searchchannellype = ref(0)
const initial = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    realTotal: 0,
})

const refreshData = () => {
    pageInfo.value.page = 1
    getData('refresh')
}

const getData = (action?: 'refresh' | 'reload' | 'switch') => {
    console.log('refreshing',refreshing.value,loading.value,condition.value)
    if (refreshing.value || loading.value) return
    if (!condition.value) return 

    if (action === 'refresh') {
        refreshing.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
    } else if (action === 'reload') {
        finished.value = false
        loading.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
        list.value = []
    } else {
        finished.value = false
        loading.value = true
    }

    aicService
        .searchAdvancedSearch({
            condition: condition.value,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            sortBy: 0
        })
        .then((res) => {
            loading.value = false
            refreshing.value = false
            const { errCode, data, total, channelType, realTotal, errMsg, lastPage } = res
            if (errCode === 0) {
                error.value = false
                list.value = mergeList(list.value, data, 'id')
                pageInfo.value.total = total
                pageInfo.value.realTotal = realTotal
                searchchannellype.value = channelType

                if (!lastPage) pageInfo.value.page += 1
                finished.value = list.value.length >= total

                if (total === 0) {
                    list.value = []
                }
            } else {
                error.value = true
                showFailToast(errMsg || '查询失败')
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                refreshing.value = false
                loading.value = false
                error.value = true
            }
        })
        .finally(() => {
            refreshing.value = false
            loading.value = false
            initial.value = true
        })
}

// watch(() =>activeTemplete.value, async () => {
//     if (activeTemplete.value) {
//         const res = await trSearchRules(activeTemplete.value.searchData.list[0])
//         condition.value = res
//         console.log('res12312312', res)
//         // 设置条件后自动获取数据
//         getData('reload')
//     }
// })
const condition = ref<conditionItem>()
const getTemplateById = () => {
    //根据id获取指定模板
    console.log('🔍 开始获取模板，templateId:', route.query.templateId)

    if (!route.query.templateId) {
        console.error('❌ templateId 为空，无法获取模板')
        return
    }

    aicService
        .searchGetTemplate({
            templateId: route.query.templateId as string,
            searchType: '1',
            page: 1,
            pageSize: 20,
        })
        .then((res) => {
            console.log('✅ API 请求成功，响应数据:', res)
            console.log('📊 返回数据长度:', res.data?.length || 0)

            if (res.data && res.data.length > 0) {
                activeTemplete.value = res.data[0]
                console.log('📋 设置 activeTemplete:', activeTemplete.value)

                // 检查 searchData 结构
                if (!activeTemplete.value.searchData) {
                    console.error('❌ activeTemplete.searchData 不存在')
                    return
                }

                if (!activeTemplete.value.searchData.list || activeTemplete.value.searchData.list.length === 0) {
                    console.error('❌ activeTemplete.searchData.list 为空或不存在')
                    return
                }

                console.log('🔄 准备调用 trSearchRules，参数:', activeTemplete.value.searchData.list[0])
                const transRes = trSearchRules(activeTemplete.value.searchData.list[0])
                console.log('✅ trSearchRules 执行完成，结果:', transRes)
                condition.value = transRes
                console.log('💾 设置 condition.value:', condition.value)
                getData('reload')
            } else {
                console.warn('⚠️ 返回数据为空，无法设置模板')
            }
        })
        .catch((error) => {
            console.error('❌ API 请求失败:', error)
        })
}

const trSearchRules = (data: ISearchConditions) => {
    const cacheHighSearchRulesData = store.state.app.hightSearchRulesData
    let traverse = (nodes: ISearchConditions[]) => {
        let res = nodes.map((node) => {
            let result: conditionItem = {
                cn: '',
                cr: '',
                cv: [],
            }

            console.log(node)

            let dataType = node.dataType

            let operator = ['number', 'date'].includes(dataType || '') ? 'BETWEEN' : node.operator

            console.log('dataType', dataType, operator)
            if (node.children && node.children.length) {
                result.cn = 'composite'
                result.cr = operator
                result.cv = traverse(node.children)
            } else {
                result.cn = node.prop
                result.cr = operator

                if (node.prop === 'industry') {
                    // console.log('1')
                    result.cv = node.valueLabel || []
                } else {
                    // console.log('2')
                    console.log(node.value)
                    let nValue = JSON.parse(JSON.stringify(node.value))
                    if (dataType === 'select') {
                        if (nValue === '1') {
                            nValue = true
                        } else {
                            nValue = false
                        }
                        //将单选值转换成布尔值
                    } else if (operator === 'BETWEEN') {
                        nValue = [`${nValue[0]}-${nValue[1]}`]
                    }
                    if (Array.isArray(nValue)) {
                        // console.log('3')
                        if (dataType === 'mapped' || node.prop === 'area') {
                            console.log('is mapped or prop is area')
                            cacheHighSearchRulesData.forEach((group: IHighSearchRules) => {
                                if (group.children) {
                                    let res = group.children.find((item) => {
                                        return item.key === node.prop
                                    })
                                    if (res && res.levelConfig) {
                                        result.cv = {}
                                        res.levelConfig.levels.forEach(
                                            (level: { name: string; title: string }, idx: number) => {
                                                console.log('nValue', nValue)
                                                let nowLevelVal = nValue.filter((vItem) => {
                                                    return vItem.length === idx + 1
                                                })
                                                if (nowLevelVal.length) {
                                                    let valArr = nowLevelVal
                                                        .map((i) => {
                                                            return i[idx]
                                                        })
                                                        .flat()
                                                    ;(result.cv as { [key: string]: unknown })[level.name] = [
                                                        ...new Set(valArr),
                                                    ]
                                                }
                                            }
                                        )
                                    }
                                }
                            })
                        } else {
                            // console.log('4')
                            // console.log('dataType',dataType )
                            // console.log('nValue',nValue)
                            // console.log('operator',operator)
                            if ((operator === 'IN' || operator === 'NOT_IN') && dataType === 'dateRangeMultiSelect') {
                                nValue = [`${nValue[0]}-${nValue[1]}`]
                                result.cv = nValue
                            } else {
                                result.cv = [...new Set(nValue.flat())]
                            }
                        }
                    } else {
                        result.cv = nValue
                    }
                }
            }
            console.log('result1', result)
            return result
        })
        console.log('result2', res)
        return res
    }
    console.log('result3',{
        cn: 'composite',
        cr: 'MUST',
        cv: traverse(data.children || []),
    })
    // 从根节点开始转换
    return {
        cn: 'composite',
        cr: 'MUST',
        cv: traverse(data.children || []),
    }
}

onMounted(async () => {
    await getTemplateById()
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}
</style>